/* ========================================
   CSS MOBILE ULTRA SIMPLES - APENAS GRID E CONTAINERS
   ======================================== */

@media screen and (max-width: 768px) {

    /* ===== APENAS GRID RESPONSIVO ===== */
    /* IMPORTANTE: Não aplicar para o grid do perfil */
    .grid-cols-2:not(#profile_navigation .grid-cols-2) {
        grid-template-columns: 1fr !important;
    }

    .grid-cols-3:not(#profile_navigation .grid-cols-3) {
        grid-template-columns: 1fr !important;
    }

    .grid-cols-4 {
        grid-template-columns: repeat(2, 1fr) !important;
    }

    /* ===== FORÇA GRID DO PERFIL ===== */
    #profile_navigation .grid-cols-2 {
        grid-template-columns: repeat(2, 1fr) !important;
        display: grid !important;
    }

    .md\:columns-2,
    .lg\:columns-3 {
        column-count: 1 !important;
    }

    /* ===== CONTAINERS PRINCIPAIS ===== */
    .container {
        padding-left: 1rem !important;
        padding-right: 1rem !important;
    }

    /* ===== OVERFLOW BÁSICO ===== */
    body {
        overflow-x: hidden !important;
    }

    /* ===== PERFIL RESPONSIVO ===== */
    /* Força layout de coluna única no perfil */
    .flex.gap-6 {
        flex-direction: column !important;
    }

    /* Garante que seções ocupem largura total */
    .w-1\/3, .w-2\/3 {
        width: 100% !important;
    }

    /* Melhora espaçamento entre seções */
    section {
        margin-bottom: 1.5rem !important;
    }

    /* Ajusta padding dos containers */
    .max-w-7xl {
        padding-left: 0.75rem !important;
        padding-right: 0.75rem !important;
    }

    /* ===== HEADER DO PERFIL RESPONSIVO ===== */
    /* Reduz altura da capa no mobile */
    #profile_header .h-64 {
        height: 12rem !important;
    }

    /* Ajusta posição da foto de perfil */
    #profile_header .-bottom-16 {
        bottom: -3rem !important;
    }

    /* Reduz tamanho da foto no mobile */
    #profile_header .w-32 {
        width: 6rem !important;
        height: 6rem !important;
    }

    /* Ajusta padding do texto */
    .pt-20 {
        padding-top: 4rem !important;
    }

    /* ===== SISTEMA DE ABAS RESPONSIVO ===== */
    /* Melhora altura mínima das abas no mobile */
    .min-h-\[400px\] {
        min-height: 300px !important;
    }

    /* Otimiza botões das abas para toque */
    [x-data] button {
        min-height: 2.5rem !important;
        touch-action: manipulation !important;
    }

    /* Melhora espaçamento das seções dentro das abas */
    [x-show] section {
        margin-bottom: 1rem !important;
    }

    /* ===== BOTÕES DE NAVEGAÇÃO DO PERFIL - GRID UNIFORME ===== */
    /* FORÇA o grid container */
    #profile_navigation .grid-cols-2 {
        display: grid !important;
        grid-template-columns: repeat(2, 1fr) !important;
        gap: 0.75rem !important;
        width: 100% !important;
    }

    /* FORÇA todos os botões do grid */
    #profile_navigation .grid-cols-2 > * {
        display: flex !important;
        flex-direction: column !important;
        align-items: center !important;
        justify-content: center !important;
        min-height: 4rem !important;
        padding: 1rem 0.5rem !important;
        border-radius: 0.5rem !important;
        transition: all 0.3s ease !important;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1) !important;
        text-align: center !important;
    }

    /* FORÇA números das estatísticas */
    #profile_navigation .text-xl {
        font-weight: 800 !important;
        font-size: 1.25rem !important;
        line-height: 1.2 !important;
        margin-bottom: 0.25rem !important;
    }

    /* FORÇA labels dos botões */
    #profile_navigation .text-xs {
        font-weight: 500 !important;
        font-size: 0.75rem !important;
        line-height: 1 !important;
    }

    /* FORÇA hover effect */
    #profile_navigation .grid-cols-2 > *:hover {
        transform: translateY(-2px) !important;
        box-shadow: 0 8px 16px rgba(0, 0, 0, 0.15) !important;
    }

    /* FORÇA botões de ação especiais */
    #profile_navigation .bg-gradient-to-br:hover {
        transform: translateY(-2px) scale(1.02) !important;
    }

}

/* ===== BREAKPOINT XS PERSONALIZADO ===== */
@media screen and (min-width: 475px) {
    .xs\:grid-cols-2 {
        grid-template-columns: repeat(2, 1fr) !important;
    }

    .xs\:inline {
        display: inline !important;
    }

    .xs\:hidden {
        display: none !important;
    }
}

/* ===== DISPOSITIVOS PEQUENOS ===== */
@media screen and (max-width: 480px) {
    .grid-cols-4 {
        grid-template-columns: 1fr !important;
    }

    /* Força botões do perfil em coluna única em telas muito pequenas */
    .xs\:grid-cols-2 {
        grid-template-columns: 1fr !important;
    }

    /* Melhora espaçamento dos botões */
    .grid.gap-2 {
        gap: 0.75rem !important;
    }

    /* Ajusta tamanho dos botões para melhor toque */
    flux\:button[size="sm"] {
        padding: 0.75rem 1rem !important;
        min-height: 2.5rem !important;
    }

    /* Oculta textos longos em telas muito pequenas */
    .xs\:inline {
        display: none !important;
    }

    .xs\:hidden {
        display: inline !important;
    }

    /* ===== ABAS EM TELAS MUITO PEQUENAS ===== */
    /* Reduz altura mínima em telas muito pequenas */
    .min-h-\[400px\] {
        min-height: 250px !important;
    }

    /* Compacta botões das abas */
    [x-data] button {
        padding: 0.5rem 0.75rem !important;
        font-size: 0.75rem !important;
    }

    /* Reduz espaçamento entre seções */
    [x-show] section {
        margin-bottom: 0.75rem !important;
    }

    /* Compacta títulos das seções */
    [x-show] h3, [x-show] h4 {
        font-size: 1rem !important;
        margin-bottom: 0.75rem !important;
    }

    /* ===== BOTÕES DE NAVEGAÇÃO EM TELAS MUITO PEQUENAS ===== */
    /* Reduz altura dos botões */
    #profile_navigation flux\:button {
        min-height: 3.5rem !important;
        padding: 0.75rem 0.5rem !important;
    }

    /* Compacta números */
    #profile_navigation .text-xl {
        font-size: 1.125rem !important;
    }

    /* Reduz gap do grid */
    #profile_navigation .grid {
        gap: 0.5rem !important;
    }

    /* Compacta ícones */
    #profile_navigation flux\:icon {
        width: 1.25rem !important;
        height: 1.25rem !important;
    }

    /* Reduz padding dos botões de ação */
    #profile_navigation .bg-gradient-to-br,
    #profile_navigation .bg-purple-600,
    #profile_navigation .bg-zinc-800 {
        padding: 0.75rem !important;
    }

    /* ===== HEADER DO PERFIL EM TELAS MUITO PEQUENAS ===== */
    /* Reduz ainda mais a altura da capa */
    #profile_header .h-64 {
        height: 10rem !important;
    }

    /* Ajusta foto de perfil para telas pequenas */
    #profile_header .w-32 {
        width: 5rem !important;
        height: 5rem !important;
    }

    #profile_header .-bottom-16 {
        bottom: -2.5rem !important;
    }

    /* Reduz padding do texto */
    .pt-20 {
        padding-top: 3.5rem !important;
    }

    /* Compacta títulos */
    .text-2xl {
        font-size: 1.5rem !important;
    }

    /* Reduz espaçamento */
    .mt-4 {
        margin-top: 0.75rem !important;
    }

    /* ===== MODAIS DE IMAGEM ===== */
    /* Otimiza modais para mobile */
    .fixed.inset-0.z-50 {
        padding: 1rem !important;
    }

    /* Reduz tamanho da foto de perfil ampliada no mobile */
    .w-80.h-80 {
        width: 16rem !important;
        height: 16rem !important;
    }

    /* Ajusta botão fechar */
    .absolute.top-4.right-4 {
        top: 1rem !important;
        right: 1rem !important;
    }

    /* Melhora informações da imagem */
    .absolute.bottom-4 {
        bottom: 1rem !important;
        left: 1rem !important;
        right: 1rem !important;
        text-align: center !important;
    }
}
